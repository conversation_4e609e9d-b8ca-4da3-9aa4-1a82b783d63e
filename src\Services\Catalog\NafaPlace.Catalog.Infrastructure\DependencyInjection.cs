using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Infrastructure.Persistence;
using NafaPlace.Catalog.Infrastructure.Services;

namespace NafaPlace.Catalog.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(
            this IServiceCollection services,
            IConfiguration configuration)
        {
            services.AddDbContext<CatalogDbContext>(options =>
                options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

            services.AddScoped<ICatalogDbContext>(provider => 
                provider.GetRequiredService<CatalogDbContext>());

            // Configuration du service d'images selon l'environnement
            var useCloudinary = configuration.GetValue<bool>("UseCloudinary", true);

            if (useCloudinary)
            {
                services.AddScoped<IProductImageService, CloudinaryImageService>();
                services.AddScoped<ICategoryImageService, CloudinaryCategoryImageService>();
            }
            else
            {
                services.AddScoped<IProductImageService, ProductImageService>();
                services.AddScoped<ICategoryImageService, CategoryImageService>();
            }

            return services;
        }
    }
}
