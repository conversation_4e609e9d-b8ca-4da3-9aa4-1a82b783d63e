# Configuration principale pour l'API Gateway
app = "nafaplace-api-gateway"
primary_region = "cdg" # Paris - proche de l'Afrique de l'Ouest

[build]
  dockerfile = "src/ApiGateways/Web/NafaPlace.ApiGateway/Dockerfile"

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ASPNETCORE_URLS = "http://+:8080"
  DOTNET_SYSTEM_GLOBALIZATION_INVARIANT = "false"
  TZ = "Africa/Conakry"
  LANG = "fr_GN.UTF-8"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = false
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

[[http_service.checks]]
  grace_period = "10s"
  interval = "30s"
  method = "GET"
  timeout = "5s"
  path = "/health"

[http_service.concurrency]
  type = "connections"
  hard_limit = 1000
  soft_limit = 800

[[vm]]
  memory = "1gb"
  cpu_kind = "shared"
  cpus = 1

[deploy]
  strategy = "rolling"
  max_unavailable = 0.33

# Variables d'environnement sensibles (à définir via fly secrets)
# FLY_DATABASE_URL sera automatiquement injectée
# STRIPE_SECRET_KEY
# JWT_SECRET_KEY
# AZURE_STORAGE_CONNECTION_STRING
