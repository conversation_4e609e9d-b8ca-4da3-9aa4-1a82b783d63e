<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <!-- Minimal dependencies for compilation but fast startup -->
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <!-- Keep project references for compilation -->
  <ItemGroup>
    <ProjectReference Include="..\..\NafaPlace.Catalog.Application\NafaPlace.Catalog.Application.csproj" />
    <ProjectReference Include="..\..\NafaPlace.Catalog.Infrastructure\NafaPlace.Catalog.Infrastructure.csproj" />
  </ItemGroup>

</Project>
