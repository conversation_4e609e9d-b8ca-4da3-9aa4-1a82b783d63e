<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <!-- Minimal dependencies for compilation but fast startup -->
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <!-- Keep project references for compilation -->
  <ItemGroup>
    <ProjectReference Include="..\NafaPlace.Payment.Application\NafaPlace.Payment.Application.csproj" />
    <ProjectReference Include="..\NafaPlace.Payment.Infrastructure\NafaPlace.Payment.Infrastructure.csproj" />
  </ItemGroup>

</Project>
