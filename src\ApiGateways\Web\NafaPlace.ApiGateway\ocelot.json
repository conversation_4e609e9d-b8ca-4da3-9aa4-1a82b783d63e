{
  "Routes": [
    // Catalog Service Routes
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-catalog-api.fly.dev",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/catalog/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "catalog"
    },

    // Payment Service Routes
    {
      "DownstreamPathTemplate": "/api/{everything}",
      "DownstreamScheme": "https",
      "DownstreamHostAndPorts": [
        {
          "Host": "nafaplace-payment-api.fly.dev",
          "Port": 443
        }
      ],
      "UpstreamPathTemplate": "/api/payments/{everything}",
      "UpstreamHttpMethod": [ "GET", "POST", "PUT", "DELETE", "OPTIONS" ],
      "Key": "payments"
    }
  ],

  "GlobalConfiguration": {
    "BaseUrl": "https://nafaplace-api-gateway.fly.dev",
    "RequestIdKey": "OcRequestId",
    "AdministrationPath": "/administration"
  }
}
